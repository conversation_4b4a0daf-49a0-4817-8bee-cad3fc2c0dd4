import { Redis } from '@upstash/redis';
import crypto from 'crypto';

/**
 * Generates a deterministic cache key from a JavaScript object
 * @param {any} obj - The object to generate a cache key from
 * @param {string} prefix - Optional prefix for the cache key
 * @returns {string} A deterministic cache key
 */
export function generateCacheKey(obj, prefix = '') {
  // Handle null/undefined
  if (obj === null || obj === undefined) {
    return prefix ? `${prefix}:null` : 'null';
  }

  // Convert object to a deterministic string representation
  const normalizedString = normalizeObject(obj);

  // Create a hash of the normalized string for consistent key length
  const hash = crypto.createHash('sha256').update(normalizedString).digest('hex');

  return prefix ? `${prefix}:${hash}` : hash;
}

/**
 * Recursively normalizes an object to create a deterministic string representation
 * @param {any} obj - The object to normalize
 * @returns {string} Normalized string representation
 */
function normalizeObject(obj) {
  if (obj === null) return 'null';
  if (obj === undefined) return 'undefined';

  const type = typeof obj;

  switch (type) {
    case 'boolean':
    case 'number':
    case 'string':
      return `${type}:${obj}`;

    case 'function':
      return `function:${obj.toString()}`;

    case 'object':
      if (obj instanceof Date) {
        return `date:${obj.toISOString()}`;
      }

      if (Array.isArray(obj)) {
        return `array:[${obj.map(normalizeObject).join(',')}]`;
      }

      // For regular objects, sort keys for deterministic output
      const sortedKeys = Object.keys(obj).sort();
      const pairs = sortedKeys.map(key => `${key}:${normalizeObject(obj[key])}`);
      return `object:{${pairs.join(',')}}`;

    default:
      return `${type}:${String(obj)}`;
  }
}

// const redis = Redis.fromEnv();
// const result = await redis.get("item");
