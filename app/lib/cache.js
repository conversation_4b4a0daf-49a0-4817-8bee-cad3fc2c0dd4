import { Redis } from '@upstash/redis';

/**
 * Generates a deterministic cache key from a JavaScript object
 * @param {any} obj - The object to generate a cache key from
 * @param {string} prefix - Optional prefix for the cache key
 * @returns {Promise<string>} A deterministic cache key
 */
export async function generateCacheKey(obj, prefix = '') {
  // Handle null/undefined
  if (obj === null || obj === undefined) {
    return prefix ? `${prefix}:null` : 'null';
  }

  // Convert object to a deterministic string representation
  const normalizedString = normalizeObject(obj);

  // Create a hash using Web Crypto API (compatible with Edge functions)
  const hash = await createHash(normalizedString);

  return prefix ? `${prefix}:${hash}` : hash;
}

/**
 * Creates a SHA-256 hash using Web Crypto API (Edge runtime compatible)
 * @param {string} data - The string to hash
 * @returns {Promise<string>} Hex-encoded hash
 */
async function createHash(data) {
  // Use Web Crypto API which is available in Edge functions
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);
  const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);

  // Convert ArrayBuffer to hex string
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

  return hashHex;
}

/**
 * Recursively normalizes an object to create a deterministic string representation
 * @param {any} obj - The object to normalize
 * @returns {string} Normalized string representation
 */
function normalizeObject(obj) {
  if (obj === null) return 'null';
  if (obj === undefined) return 'undefined';

  const type = typeof obj;

  switch (type) {
    case 'boolean':
    case 'number':
    case 'string':
      return `${type}:${obj}`;

    case 'function':
      return `function:${obj.toString()}`;

    case 'object':
      if (obj instanceof Date) {
        return `date:${obj.toISOString()}`;
      }

      if (Array.isArray(obj)) {
        return `array:[${obj.map(normalizeObject).join(',')}]`;
      }

      // For regular objects, sort keys for deterministic output
      const sortedKeys = Object.keys(obj).sort();
      const pairs = sortedKeys.map(key => `${key}:${normalizeObject(obj[key])}`);
      return `object:{${pairs.join(',')}}`;

    default:
      return `${type}:${String(obj)}`;
  }
}

// const redis = Redis.fromEnv();
// const result = await redis.get("item");
